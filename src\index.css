@import url('https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700&family=Manrope:wght@300;400;500;600;700&display=swap');

html {
  scroll-behavior: smooth;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 225 30% 6%;
    --foreground: 0 0% 98%;

    --card: 225 30% 8%;
    --card-foreground: 0 0% 98%;

    --popover: 225 30% 6%;
    --popover-foreground: 0 0% 98%;

    --primary: 192 100% 50%;
    --primary-foreground: 225 30% 5%;

    --secondary: 270 75% 59%;
    --secondary-foreground: 0 0% 98%;

    --muted: 225 25% 15%;
    --muted-foreground: 215 20% 75%;

    --accent: 270 75% 59%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 225 30% 15%;
    --input: 225 30% 15%;
    --ring: 192 100% 50%;

    --radius: 0.75rem;

    --glow-primary: 192 100% 60%;
    --glow-secondary: 270 75% 65%;

    --sidebar-background: 225 30% 6%;
    --sidebar-foreground: 0 0% 98%;
    --sidebar-primary: 192 100% 50%;
    --sidebar-primary-foreground: 225 30% 5%;
    --sidebar-accent: 225 30% 10%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 225 30% 15%;
    --sidebar-ring: 192 100% 50%;
  }
}

@layer base {
  * {
    @apply border-border selection:bg-neon-purple/20 selection:text-white;
  }

  body {
    @apply bg-background text-foreground font-outfit overflow-x-hidden;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02", "cv03";
  }

  html {
    @apply scroll-smooth;
  }
}

@layer utilities {
  .scrollbar-none {
    -ms-overflow-style: none;
    scrollbar-width: none; /* Firefox */
    /* Fallback for older browsers */
    overflow: -moz-scrollbars-none;
  }

  .scrollbar-none::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
  }

  .glass-card {
    @apply backdrop-blur-lg bg-white/5 border border-white/10 shadow-[0_4px_12px_-2px_rgba(0,0,0,0.3)];
    @apply transition-all duration-300 ease-in-out;
  }

  .glass-card:hover {
    @apply border-primary/40 shadow-[0_8px_20px_-4px_rgba(var(--primary),0.3)];
  }

  .glass-card-soft {
    @apply backdrop-blur-lg bg-white/5 shadow-[0_4px_12px_-2px_rgba(0,0,0,0.3)];
    @apply transition-all duration-300 ease-in-out;
  }

  .hover\:shadow-neon-glow:hover {
    @apply shadow-[0_8px_32px_rgba(0,230,255,0.15)];
  }

  .premium-glass {
    @apply backdrop-blur-xl bg-gradient-glass border border-white/20;
    @apply shadow-[0_8px_32px_-8px_rgba(0,0,0,0.4),0_0_0_1px_rgba(255,255,255,0.05)];
    @apply transition-all duration-500 ease-out;
  }

  .premium-glass:hover {
    @apply border-premium-gold/30 shadow-[0_12px_40px_-8px_rgba(255,215,0,0.2)];
    transform: translateY(-2px);
  }

  .luxury-card {
    @apply relative overflow-hidden rounded-2xl;
    @apply bg-gradient-to-br from-white/10 to-white/5;
    @apply border border-white/20 backdrop-blur-xl;
    @apply shadow-[0_8px_32px_-8px_rgba(0,0,0,0.3)];
    @apply transition-all duration-500 ease-out;
  }

  .luxury-card::before {
    content: '';
    @apply absolute inset-0 opacity-0 transition-opacity duration-500;
    background: linear-gradient(135deg, rgba(255,215,0,0.1), rgba(192,192,192,0.1));
  }

  .luxury-card:hover::before {
    @apply opacity-100;
  }

  .luxury-card:hover {
    @apply border-premium-gold/40 shadow-[0_16px_48px_-8px_rgba(255,215,0,0.3)];
    transform: translateY(-4px) scale(1.02);
  }

  .neon-glow {
    @apply relative;
  }

  .neon-glow::after {
    content: "";
    @apply absolute inset-0 bg-gradient-neon opacity-30 blur-lg -z-10;
  }

  .text-gradient {
    @apply bg-gradient-neon bg-clip-text text-transparent;
  }

  .text-gradient-premium {
    @apply bg-gradient-premium bg-clip-text text-transparent;
  }

  .text-gradient-luxury {
    @apply bg-gradient-luxury bg-clip-text text-transparent;
  }

  .text-gradient-aurora {
    @apply bg-gradient-aurora bg-clip-text text-transparent;
  }

  .premium-button {
    @apply relative overflow-hidden rounded-xl px-8 py-4;
    @apply bg-gradient-premium text-black font-semibold;
    @apply shadow-[0_4px_20px_rgba(255,215,0,0.3)];
    @apply transition-all duration-300 ease-out;
    @apply hover:shadow-[0_8px_30px_rgba(255,215,0,0.5)];
    @apply hover:scale-105 active:scale-95;
  }

  .premium-button::before {
    content: '';
    @apply absolute inset-0 opacity-0 transition-opacity duration-300;
    background: linear-gradient(135deg, rgba(255,255,255,0.2), transparent);
  }

  .premium-button:hover::before {
    @apply opacity-100;
  }

  .section-padding {
    @apply py-20 md:py-28 px-4 md:px-8;
  }

  .reveal {
    @apply opacity-0 transition-all duration-700 ease-out;
  }

  .reveal.active {
    @apply opacity-100 transform-none;
  }

  /* Generic Button Hover Enhancements - applies to elements with role='button' or common button classes */
  .btn, button, [role='button'], input[type='submit'], input[type='button'] {
    @apply transition-all duration-200 ease-in-out;
  }
  .btn:hover, button:hover, [role='button']:hover, input[type='submit']:hover, input[type='button']:hover {
    @apply scale-[1.03] brightness-110;
  }

  /* Icon Hover Enhancements - targets SVGs directly, can be more specific if needed */
  svg {
    @apply transition-all duration-200 ease-in-out;
  }
  svg:hover {
    @apply scale-110 opacity-85;
  }

  /* Hero image specific styles */
  .hero-image-container {
    @apply w-full h-full flex items-center justify-center overflow-visible;
  }

  .hero-image {
    @apply transition-all duration-500 ease-out;
    animation: image-fade-in 1.2s ease-out forwards;
  }

  .hero-spotlight {
    @apply rounded-full bg-gradient-to-br from-neon-cyan/30 to-neon-purple/30;
    width: 200%;
    height: 200%;
    top: -50%;
    left: -50%;
    /* animation: spotlight-float 8s ease-in-out infinite alternate; */
  }

  /* Services section specific animations */
  .card-glow {
    @apply absolute opacity-0 transition-opacity inset-0;
    background: radial-gradient(ellipse at center, rgba(0, 255, 255, 0.15) 0%, rgba(157, 78, 221, 0.15) 50%, transparent 70%);
    /* animation: pulse-glow 4s ease-in-out infinite; */
  }

  .services-grid {
    @apply grid gap-8 relative;
    perspective: 1000px;
  }

  .services-card {
    @apply relative rounded-2xl overflow-hidden;
    transform-style: preserve-3d;
    transition: transform 0.5s ease-out;
  }

  .services-card:hover {
    transform: translateZ(20px);
  }

  @keyframes pulse-glow {
    0%, 100% {
      opacity: 0.3;
      transform: scale(0.95);
    }
    50% {
      opacity: 0.6;
      transform: scale(1);
    }
  }

  @keyframes image-fade-in {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes spotlight-float {
    0% {
      transform: translateX(-5%) translateY(-5%) rotate(0deg);
      opacity: 0.5;
    }
    50% {
      opacity: 0.7;
    }
    100% {
      transform: translateX(5%) translateY(5%) rotate(5deg);
      opacity: 0.5;
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes pulse {
    0%, 100% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(0, 255, 255, 0.4);
    }
    50% {
      transform: scale(1.03);
      box-shadow: 0 0 10px 5px rgba(0, 255, 255, 0.1), 0 0 15px 10px rgba(0, 255, 255, 0.05);
    }
  }

  .cta-pulse-hover:hover {
    animation: pulse 1.5s infinite;
  }

  /* Additional animation classes */
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-pulse-glow {
    /* animation: pulse-glow 4s ease-in-out infinite; */
  }

  .drop-shadow-glow {
    filter: drop-shadow(0 0 10px rgba(0, 229, 229, 0.5));
  }

  /* Orbital animation system optimizations */
  .orbital-icon {
    transform-origin: center;
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* Performance optimizations for orbital animations */
  @supports (will-change: transform) {
    .orbital-icon {
      will-change: transform;
    }
  }

  /* Additional orbital animation keyframes */
  @keyframes orbital-clockwise {
    from {
      transform: rotate(0deg) translateX(var(--radius)) rotate(0deg);
    }
    to {
      transform: rotate(360deg) translateX(var(--radius)) rotate(-360deg);
    }
  }

  @keyframes orbital-counterclockwise {
    from {
      transform: rotate(0deg) translateX(var(--radius)) rotate(0deg);
    }
    to {
      transform: rotate(-360deg) translateX(var(--radius)) rotate(360deg);
    }
  }

  @keyframes figure-eight {
    0% { transform: translate(0, 0) scale(1); }
    25% { transform: translate(var(--radius-x), calc(var(--radius-y) * -0.5)) scale(1.1); }
    50% { transform: translate(0, 0) scale(1); }
    75% { transform: translate(calc(var(--radius-x) * -1), calc(var(--radius-y) * 0.5)) scale(1.1); }
    100% { transform: translate(0, 0) scale(1); }
  }

  /* Accessibility: Respect reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .orbital-icon,
    .animate-float,
    .animate-pulse-glow,
    .hero-spotlight,
    .card-glow,
    .pricing-card-glow,
    .animate-fade-in,
    .animate-fade-up,
    .animate-text-reveal,
    .animate-pulse-subtle,
    .animate-chevron-1,
    .animate-chevron-2,
    .animate-chevron-3 {
      animation: none !important;
      transform: none !important;
    }

    .orbital-icon * {
      animation: none !important;
      transform: none !important;
    }
  }

  /* Enhanced z-index utilities */
  .z-5 {
    z-index: 5;
  }

  /* Pricing section specific optimizations */
  .pricing-orbital-container {
    transform-style: preserve-3d;
    perspective: 1000px;
  }

  /* Enhanced pricing card animations */
  .pricing-card-glow {
    @apply absolute opacity-0 transition-opacity inset-0;
    background: radial-gradient(ellipse at center, rgba(255, 215, 0, 0.1) 0%, rgba(0, 255, 255, 0.1) 50%, transparent 70%);
    /* animation: pricing-pulse-glow 6s ease-in-out infinite; */
  }

  /* Hero section animations */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes fadeUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  @keyframes textReveal {
    from { clip-path: inset(0 100% 0 0); }
    to { clip-path: inset(0 0 0 0); }
  }
  
  @keyframes pulseSubtle {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(0.98); }
  }
  
  @keyframes chevronBounce1 {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(8px); }
  }
  
  @keyframes chevronBounce2 {
    0%, 100% { transform: translateY(8px); }
    50% { transform: translateY(16px); }
  }
  
  @keyframes chevronBounce3 {
    0%, 100% { transform: translateY(16px); }
    50% { transform: translateY(24px); }
  }

  .animate-fade-in {
    animation: fadeIn 0.8s ease-out forwards;
  }
  
  .animate-fade-up {
    animation: fadeUp 0.8s ease-out forwards;
  }
  
  .animate-text-reveal {
    animation: textReveal 1s cubic-bezier(0.77, 0, 0.175, 1) forwards;
    display: inline-block;
  }
  
  .animate-pulse-subtle {
    animation: pulseSubtle 2s ease-in-out infinite;
  }
  
  .animate-chevron-1 {
    animation: chevronBounce1 1.5s ease-in-out infinite;
  }
  
  .animate-chevron-2 {
    animation: chevronBounce2 1.5s ease-in-out infinite;
  }
  
  .animate-chevron-3 {
    animation: chevronBounce3 1.5s ease-in-out infinite;
  }

  @keyframes pricing-pulse-glow {
    0%, 100% {
      opacity: 0.2;
      transform: scale(0.98);
    }
    50% {
      opacity: 0.4;
      transform: scale(1.02);
    }
  }

  /* Responsive orbital animations for pricing */
  @media (max-width: 768px) {
    .pricing-orbital-container {
      transform: scale(0.7);
    }
  }

  /* Static pricing card tag positioning - isolated from card transforms */
  .pricing-tag-static {
    position: absolute !important;
    top: 0 !important;
    left: 50% !important;
    transform: translateX(-50%) translateY(-50%) !important;
    will-change: auto !important;
  }

  /* Pricing card container with conditional top margin for tags */
  .pricing-card-container {
    position: relative;
  }

  .pricing-card-container-with-tag {
    position: relative;
    margin-top: 16px;
  }

  @media (max-width: 480px) {
    .pricing-orbital-container {
      transform: scale(0.5);
    }
  }

  /* Enhanced mobile navigation styles */
  @supports (padding: max(0px)) {
    .safe-area-inset-top {
      padding-top: max(1rem, env(safe-area-inset-top));
    }
    .safe-area-inset-right {
      padding-right: max(1rem, env(safe-area-inset-right));
    }
  }

  /* Ensure mobile menu is always above other content with proper z-index hierarchy */
  .mobile-menu-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 9990 !important;
  }

  .mobile-menu-button {
    position: fixed !important;
    z-index: 9999 !important;
    top: 1rem !important;
    right: 1rem !important;
    /* Ensure button is always visible and perfectly centered */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  /* Force mobile menu button visibility on small screens - Enhanced */
  @media (max-width: 767px) {
    .mobile-menu-button {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      visibility: visible !important;
      opacity: 1 !important;
      position: fixed !important;
      top: 1rem !important;
      right: 1rem !important;
      z-index: 9999 !important;
      /* Ensure button stays within viewport */
      max-width: calc(100vw - 2rem) !important;
      max-height: calc(100vh - 2rem) !important;
    }

    /* Hide desktop navigation on mobile */
    nav[class*="hidden md:flex"] {
      display: none !important;
    }

    /* Ensure navbar desktop is completely hidden on mobile */
    .navbar-desktop {
      display: none !important;
    }
  }

  /* Improved touch targets for mobile */
  @media (max-width: 768px) {
    button, [role="button"], .touch-target {
      min-height: 44px;
      min-width: 44px;
    }
  }

  /* Ensure expertise section content is always visible */
  #expertise .grid > div {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
  }

  /* Fallback for motion components */
  [data-framer-component-type] {
    opacity: 1 !important;
    visibility: visible !important;
  }

  /* Z-index hierarchy for navigation components */
  .navbar-desktop {
    z-index: 50;
  }

  .navbar-mobile-button {
    z-index: 9999;
  }

  .navbar-mobile-overlay {
    z-index: 9990;
  }

  .navbar-mobile-menu {
    z-index: 9995;
  }

  /* Prevent any background elements from interfering with navigation */
  .navbar-desktop,
  .navbar-mobile-button,
  .navbar-mobile-overlay,
  .navbar-mobile-menu {
    pointer-events: auto;
  }

  /* Ensure ambient glow effects don't interfere with navigation */
  .fixed[class*="blur-3xl"] {
    z-index: 1;
    pointer-events: none;
  }

  /* Additional mobile navigation fixes */
  @media (max-width: 767px) {
    /* Prevent any element from overlapping mobile menu button */
    * {
      z-index: auto;
    }

    .mobile-menu-button {
      z-index: 9999 !important;
    }

    .navbar-mobile-overlay {
      z-index: 9990 !important;
    }

    .navbar-mobile-menu {
      z-index: 9995 !important;
    }

    /* Ensure no horizontal overflow on mobile devices */
    html, body {
      overflow-x: hidden !important;
      max-width: 100vw !important;
    }

    /* Prevent decorative elements from causing overflow */
    section {
      max-width: 100vw !important;
      overflow-x: hidden !important;
    }
  }

  /* Desktop navigation hover effect improvements */
  @media (min-width: 768px) {
    .navbar-desktop {
      /* Prevent background bleed and duplication */
      isolation: isolate;
      contain: layout style;
    }

    /* Ensure desktop navigation doesn't create visual artifacts */
    .navbar-desktop::before,
    .navbar-desktop::after {
      display: none;
    }
  }
}
